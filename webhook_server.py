#!/usr/bin/env python3
"""
Servidor de prueba para recibir webhooks de LumusAI.
Ejecuta este servidor para verificar que los webhooks se envían correctamente.
"""

from fastapi import FastAPI, Request
import uvicorn
import json
from datetime import datetime

app = FastAPI(title="Webhook Test Server")

# Lista para almacenar webhooks recibidos
received_webhooks = []

@app.post("/webhook")
async def receive_webhook(request: Request):
    """Recibe webhooks de LumusAI."""
    try:
        payload = await request.json()
        
        # Agregar timestamp de recepción
        payload["received_at"] = datetime.utcnow().isoformat()
        received_webhooks.append(payload)
        
        # Imprimir información del webhook
        print("\n" + "="*60)
        print("🔔 WEBHOOK RECIBIDO!")
        print("="*60)
        print(f"📋 Task ID: {payload.get('task_id', 'N/A')}")
        print(f"📊 Status: {payload.get('status', 'N/A')}")
        print(f"⏱️  Processing Time: {payload.get('processing_time', 'N/A')} seconds")
        print(f"🕐 Received At: {payload['received_at']}")
        
        if payload.get('status') == 'completed':
            print("✅ PROCESAMIENTO COMPLETADO EXITOSAMENTE!")
            data = payload.get('data', {})
            if isinstance(data, dict):
                print(f"📄 Result Keys: {list(data.keys())}")
        elif payload.get('status') == 'failed':
            print("❌ PROCESAMIENTO FALLÓ")
            error = payload.get('data', {}).get('error', 'Unknown error')
            print(f"🚨 Error: {error}")
        
        print(f"📝 Full Payload:")
        print(json.dumps(payload, indent=2, ensure_ascii=False))
        print("="*60)
        
        return {
            "status": "received",
            "message": f"Webhook for task {payload.get('task_id')} received successfully",
            "received_at": payload["received_at"]
        }
        
    except Exception as e:
        print(f"❌ Error processing webhook: {e}")
        return {"status": "error", "message": str(e)}

@app.get("/webhooks")
async def list_received_webhooks():
    """Lista todos los webhooks recibidos."""
    return {
        "total_webhooks": len(received_webhooks),
        "webhooks": received_webhooks
    }

@app.get("/webhooks/latest")
async def get_latest_webhook():
    """Obtiene el último webhook recibido."""
    if received_webhooks:
        return received_webhooks[-1]
    return {"message": "No webhooks received yet"}

@app.get("/")
async def root():
    """Página principal del servidor de prueba."""
    return {
        "message": "Webhook Test Server is running!",
        "endpoints": {
            "receive_webhook": "POST /webhook",
            "list_webhooks": "GET /webhooks", 
            "latest_webhook": "GET /webhooks/latest"
        },
        "total_received": len(received_webhooks)
    }

if __name__ == "__main__":
    print("🚀 Starting Webhook Test Server...")
    print("📡 Webhook URL: http://localhost:8001/webhook")
    print("📋 View received webhooks: http://localhost:8001/webhooks")
    print("🔄 Press Ctrl+C to stop")
    
    uvicorn.run(app, host="0.0.0.0", port=8001)
