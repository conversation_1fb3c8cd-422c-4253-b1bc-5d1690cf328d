import requests
from typing import Dict
from openai import AzureOpenAI, OpenAIError, APIConnectionError, AuthenticationError
import instructor
from pydantic import BaseModel
from fastapi.responses import JSONResponse

class OpenAIClient:
    def __init__(self, api_key: str, api_version: str, azure_endpoint: str, model: str):
        self.api_key = api_key
        self.api_version = api_version
        self.azure_endpoint = azure_endpoint
        self.model = model



    def headers_preparation(self) -> Dict[str, str]:
        headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }
        return headers



    def call_openai(self, payload: dict) -> dict:

        if not isinstance(payload, dict):
            raise ValueError("The 'payload' argument must be a dictionary.")
        

        headers = self.headers_preparation()

            # Validate the headers
        if not isinstance(headers, dict) or "api-key" not in headers:
            raise ValueError("Headers must be a dictionary containing an 'api-key'.")
        
            # Validate the endpoint components
        if not self.azure_endpoint or not isinstance(self.azure_endpoint, str):
            raise ValueError("The 'azure_endpoint' must be a valid non-empty string.")

        if not self.model or not isinstance(self.model, str):
            raise ValueError("The 'model' must be a valid non-empty string.")

        if not self.api_version or not isinstance(self.api_version, str):
            raise ValueError("The 'api_version' must be a valid non-empty string.")
        
        endpoint = f"{self.azure_endpoint}openai/deployments/{self.model}/chat/completions?api-version={self.api_version}"

        try:
            response = requests.post(endpoint, headers=headers, json=payload)
            response.raise_for_status()

        except requests.exceptions.HTTPError as http_err:
            raise Exception(f"HTTP error occurred: {http_err} - Response: {response.text}")
        except requests.exceptions.ConnectionError:
            raise Exception("Connection error occurred while attempting to reach the OpenAI API.")
        except requests.exceptions.Timeout:
            raise Exception("The request to the OpenAI API timed out.")
        except requests.exceptions.RequestException as e:
            raise Exception(f"An error occurred while making the request: {e}")
        
        try:
            response_json = response.json()

        except ValueError:
            raise Exception("Failed to parse the response as JSON.")
        
        return response_json
    


    def call_instructor(self, structure: type[BaseModel], data: str, prompt: str):
        if not issubclass(structure, BaseModel):
          return JSONResponse(content={"error": "The 'model' argument must be a class that inherits from BaseModel"}, status_code=400)

        if not isinstance(data, str):
          return JSONResponse(content={"error": "The 'data' argument must be a valid string"}, status_code=400)

        if not isinstance(prompt, str) or not prompt.strip():
          return JSONResponse(content={"error": "The 'prompt' argument must be a valid string"}, status_code=400)
        
        try:
            response = instructor.from_openai(AzureOpenAI(api_key=self.api_key, api_version=self.api_version, azure_endpoint=self.azure_endpoint)).chat.completions.create(
                model= self.model,
                response_model=structure,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    },
                    {
                        "role": "user",
                        "content": data
                    }
                ]
            )

            result = response.model_dump_json(indent=2)
            return result
        

        except AuthenticationError as e:
            # Authentication error with the API
            return JSONResponse(
                content={"error": "Authentication error in the OpenAI API", "details": str(e)},
                status_code=401
            )

        except APIConnectionError as e:
            # Connection error with the API
            return JSONResponse(
                content={"error": "Could not connect to the OpenAI API", "details": str(e)},
                status_code=503
            )

        except OpenAIError as e:
            # Other errors related to the OpenAI API
            return JSONResponse(
                content={"error": "Error in the call to the OpenAI API", "details": str(e)},
                status_code=500
            )

        except Exception as e:
            # Capture any other unexpected error
            return JSONResponse(
                content={"error": "An unexpected error occurred", "details": str(e)},
                status_code=500
            )
        
    def prepare_payload(self, base64_image_data: str, prompt: str) -> dict:
        # Prepara el payload para la primera llamada a OpenAI
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": base64_image_data
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 6000
        }
        return payload