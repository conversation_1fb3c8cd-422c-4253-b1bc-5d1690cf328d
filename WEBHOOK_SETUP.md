# 🔔 LumusAI Webhook Setup Guide

This guide shows how to set up and use the webhook callback system for LumusAI document processing.

## 📋 **What's New**

The `/process` endpoint now supports an optional `webhook_url` parameter that will receive a notification when document processing completes (success or failure).

## 🚀 **Quick Setup**

### 1. **Install Additional Dependencies**

```bash
pip install aiohttp==3.10.10 aiosignal==1.3.1
```

Or install from the webhook requirements file:
```bash
pip install -r requirements_webhook.txt
```

### 2. **Start the Service**

```bash
uvicorn main:app --reload
```

## 📡 **Using Webhooks**

### **Submit Document with Webhook**

```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@path/to/resume.pdf" \
     -F "webhook_url=https://your-app.com/webhook"
```

### **Webhook Payload Format**

When processing completes, your webhook URL will receive a POST request with this payload:

**Success:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "personal_info": {
      "full_name": "John Doe",
      "email": "<EMAIL>"
    },
    "skills": [
      {
        "name": "Python",
        "years_of_experience": 5.0
      }
    ],
    "work_experience": [...]
  },
  "processing_time": 245.6
}
```

**Failure:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "failed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "error": "Error processing document: Invalid file format"
  },
  "processing_time": 12.3
}
```

## 🧪 **Testing Webhooks**

### **Use Built-in Test Endpoint**

1. **Get test webhook URL:**
```bash
curl -X GET "http://localhost:8000/webhook/test-url"
```

2. **Use the returned URL in your process request:**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "webhook_url=http://localhost:8000/webhook/test"
```

3. **Check the logs** to see the webhook notification received.

### **Test with Your Own Webhook**

Create a simple webhook receiver:

```python
from fastapi import FastAPI, Request
import json

app = FastAPI()

@app.post("/webhook")
async def receive_webhook(request: Request):
    payload = await request.json()
    print(f"Webhook received: {json.dumps(payload, indent=2)}")
    return {"status": "received"}

# Run with: uvicorn webhook_receiver:app --port 8001
```

Then use `http://localhost:8001/webhook` as your webhook URL.

## 🔧 **Webhook Features**

### **Automatic Retries**
- **3 retry attempts** with exponential backoff
- **30-second timeout** per request
- Failed webhooks are logged but don't affect processing

### **Status Types**
- `completed` - Processing finished successfully
- `failed` - Processing failed with error
- `cancelled` - Processing was cancelled (rare)

### **Processing Time**
- Webhook includes actual processing duration in seconds
- Useful for monitoring and optimization

## 🛠️ **Integration Examples**

### **Update Database on Completion**

```python
@app.post("/webhook/document-processed")
async def handle_document_completion(payload: dict):
    task_id = payload["task_id"]
    status = payload["status"]
    
    if status == "completed":
        # Save results to database
        result_data = payload["data"]
        await save_processing_result(task_id, result_data)
        
        # Notify user
        await notify_user_completion(task_id)
    else:
        # Handle failure
        error = payload["data"]["error"]
        await log_processing_error(task_id, error)
    
    return {"status": "processed"}
```

### **Send Email Notification**

```python
@app.post("/webhook/email-notification")
async def send_email_notification(payload: dict):
    task_id = payload["task_id"]
    status = payload["status"]
    processing_time = payload["processing_time"]
    
    if status == "completed":
        await send_email(
            subject=f"Document {task_id} processed successfully",
            body=f"Processing completed in {processing_time:.1f} seconds"
        )
    
    return {"status": "email_sent"}
```

## 🔒 **Security Considerations**

1. **Use HTTPS** for webhook URLs in production
2. **Validate webhook payloads** in your receiver
3. **Implement authentication** if needed (API keys, signatures)
4. **Rate limit** your webhook endpoints
5. **Log webhook events** for monitoring

## 📊 **Monitoring**

Check the application logs for webhook activity:
```bash
# Look for webhook-related log entries
tail -f logs/app.log | grep -i webhook
```

Log entries include:
- Webhook sending attempts
- Success/failure status
- Retry attempts
- Error details

## 🚨 **Troubleshooting**

### **Webhook Not Received**
1. Check if webhook URL is accessible
2. Verify URL format (must include http/https)
3. Check firewall/network settings
4. Review application logs for errors

### **Webhook Timeouts**
1. Ensure webhook endpoint responds quickly (< 30s)
2. Use async processing in webhook receiver
3. Return response immediately, process in background

### **Missing Dependencies**
```bash
# Install webhook dependencies
pip install aiohttp aiosignal
```

## 📈 **Performance Notes**

- Webhooks are sent **asynchronously** and don't block processing
- Failed webhooks are retried in background
- Processing continues normally even if webhook fails
- No impact on existing synchronous processing flow

---

**🎉 That's it!** Your LumusAI service now supports webhook notifications for document processing completion.
