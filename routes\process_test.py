# process_test.py

from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, UploadFile, Form, File, Request

from services.processors.smarthr.cv_processor_test import CVProcessorTest
from services.processors.papirus.tutela_contestacion_processor import TutelaContestacionProcessor
from services.processors.papirus.tutela_fallo_processor import TutelaFalloProcessor
from services.processors.papirus.tutela_desacato_processor import TutelaDesacatoProcessor
from services.processors.papirus.tutela_correo_processor import TutelaCorreoProcessor
from services.processors.facturius.invoice_processor_test import InvoiceProcessorTest
from routes.task_management import store_task
from utils.simple_background import SimpleBackgroundProcessor

router = APIRouter()

# Global background processor instance
simple_background = SimpleBackgroundProcessor()

async def _sync_task_status(task_id: str, bg_processor: SimpleBackgroundProcessor):
    """Sync task status from simple_background to task_management."""
    import asyncio
    from routes.task_management import update_task_status

    # Poll for task completion
    while True:
        task_info = bg_processor.get_task_status(task_id)
        if not task_info:
            break

        status = task_info.get("status")
        if status == "completed":
            update_task_status(
                task_id=task_id,
                status="completed",
                result=task_info.get("result"),
                processing_time=task_info.get("processing_time"),
                completed_at=task_info.get("completed_at")
            )
            break
        elif status == "failed":
            update_task_status(
                task_id=task_id,
                status="failed",
                error=task_info.get("error"),
                processing_time=task_info.get("processing_time"),
                failed_at=task_info.get("completed_at")
            )
            break

        # Wait before checking again
        await asyncio.sleep(1)

def get_processor_mapping(openai_client, langchain_client):
    return {
        "invoice": InvoiceProcessorTest(openai_client, langchain_client),
        "cv": CVProcessorTest(openai_client, langchain_client),
        "tutela_contestacion": TutelaContestacionProcessor(openai_client, langchain_client),
        "tutela_correo_contestacion": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_fallo": TutelaFalloProcessor(openai_client, langchain_client),
        "tutela_correo_fallo": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_desacato": TutelaDesacatoProcessor(openai_client, langchain_client),
        "tutela_correo_desacato": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_correo": TutelaCorreoProcessor(openai_client, langchain_client),
    }

@router.post("/process-test", summary="Process a document asynchronously with callback")
async def process_test_request(
    request: Request,
    action: str = Form(...),
    callback_url: str = Form(...),
    file: UploadFile = File(None),
    data: str = Form(None)
):
    logger = request.app.state.logger
    logger.info(f"Async process-test request for action: {action}")

    if not file and not data:
        raise HTTPException(status_code=400, detail="A file or text is required.")
    if file and data:
        raise HTTPException(status_code=400, detail="Send either a text or a file, not both.")

    openai_client = request.app.state.openai_client
    langchain_client = request.app.state.langchain_client

    processor_mapping = get_processor_mapping(openai_client, langchain_client)
    processor = processor_mapping.get(action.lower())

    if not processor:
        raise HTTPException(status_code=400, detail="Invalid Action.")

    try:
        # Use the working simple_background approach for file handling
        task_id = await simple_background.submit_task(
            action=action.lower(),
            processor=processor,
            file=file,
            data=data,
            webhook_url=callback_url,
            logger=logger
        )

        logger.info(f"Task {task_id} submitted successfully")

        # Store task information in our task management system
        file_name = getattr(file, "filename", None) if file else str(data)[:50] if data else "No file"
        store_task(
            task_id=task_id,
            action=action.lower(),
            status="processing",
            file_name=file_name,
            callback_url=callback_url
        )

        # Create a background task to sync status updates
        import asyncio
        asyncio.create_task(_sync_task_status(task_id, simple_background))

        return {
            "status": "accepted",
            "message": "Processing started",
            "task_id": task_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Error submitting background task: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error starting background processing: {str(e)}")


