import asyncio
import time
import uuid
import tempfile
import os
from typing import Optional, Dict, Any
from fastapi import UploadFile
from utils.webhook_sender import webhook_sender


class BackgroundProcessor:
    """
    Simple background processor for document processing tasks.
    Handles task tracking and background execution without external dependencies.
    """
    
    def __init__(self):
        # In-memory task storage (you could replace with <PERSON><PERSON> later)
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.completed_tasks: Dict[str, Dict[str, Any]] = {}
    
    def generate_task_id(self, action: str) -> str:
        """Generate unique task ID."""
        timestamp = int(time.time())
        unique_id = uuid.uuid4().hex[:8]
        return f"{action}-{timestamp}-{unique_id}"
    
    async def submit_task(
        self, 
        action: str, 
        processor, 
        file: Optional[UploadFile] = None, 
        data: Optional[str] = None,
        webhook_url: Optional[str] = None,
        logger = None
    ) -> str:
        """
        Submit a task for background processing.
        
        Args:
            action: Processing action type
            processor: Processor instance to use
            file: File to process
            data: URL or text data to process
            webhook_url: Optional webhook URL
            logger: Logger instance
            
        Returns:
            str: Task ID
        """
        task_id = self.generate_task_id(action)
        
        # Store task info
        self.active_tasks[task_id] = {
            "task_id": task_id,
            "action": action,
            "status": "processing",
            "created_at": time.time(),
            "webhook_url": webhook_url,
            "file_name": getattr(file, "filename", None) if file else str(data)[:50] if data else "No file"
        }
        
        # Prepare file data for background processing
        file_path = None
        if file:
            # Save file to temporary location
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}")
            content = await file.read()
            temp_file.write(content)
            temp_file.close()
            file_path = temp_file.name
        
        # Start background task
        asyncio.create_task(
            self._process_in_background(
                task_id, action, processor, file_path, data, webhook_url, logger
            )
        )
        
        if logger:
            logger.info(f"Background task {task_id} submitted for action: {action}")
        
        return task_id
    
    async def _process_in_background(
        self, 
        task_id: str, 
        action: str, 
        processor, 
        file_path: Optional[str], 
        data: Optional[str],
        webhook_url: Optional[str],
        logger
    ):
        """
        Process document in background.
        """
        start_time = time.time()
        
        try:
            if logger:
                logger.info(f"Starting background processing for task {task_id}")
            
            # Create mock UploadFile if we have a file path
            file_obj = None
            if file_path:
                class MockUploadFile:
                    def __init__(self, path: str):
                        self.file_path = path
                        self.filename = os.path.basename(path)
                        self.content_type = "application/octet-stream"
                    
                    async def read(self):
                        with open(self.file_path, 'rb') as f:
                            return f.read()
                
                file_obj = MockUploadFile(file_path)
            
            # Process the document
            result = await processor.process(file_obj, data)
            processing_time = time.time() - start_time
            
            # Store completed task
            self.completed_tasks[task_id] = {
                "task_id": task_id,
                "action": action,
                "status": "completed",
                "result": result,
                "processing_time": processing_time,
                "completed_at": time.time()
            }
            
            # Remove from active tasks
            self.active_tasks.pop(task_id, None)
            
            # Send webhook if provided
            if webhook_url:
                webhook_sender.send_webhook_background(
                    webhook_url=webhook_url,
                    task_id=task_id,
                    status="completed",
                    data=result,
                    processing_time=processing_time
                )
            
            if logger:
                logger.info(f"Background task {task_id} completed successfully in {processing_time:.2f}s")
        
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            
            # Store failed task
            self.completed_tasks[task_id] = {
                "task_id": task_id,
                "action": action,
                "status": "failed",
                "error": error_msg,
                "processing_time": processing_time,
                "completed_at": time.time()
            }
            
            # Remove from active tasks
            self.active_tasks.pop(task_id, None)
            
            # Send failure webhook if provided
            if webhook_url:
                webhook_sender.send_webhook_background(
                    webhook_url=webhook_url,
                    task_id=task_id,
                    status="failed",
                    data={"error": error_msg},
                    processing_time=processing_time
                )
            
            if logger:
                logger.error(f"Background task {task_id} failed after {processing_time:.2f}s: {error_msg}")
        
        finally:
            # Clean up temporary file
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as cleanup_error:
                    if logger:
                        logger.warning(f"Could not remove temp file {file_path}: {cleanup_error}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status."""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        return None
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task result if completed."""
        if task_id in self.completed_tasks:
            task_info = self.completed_tasks[task_id]
            if task_info["status"] == "completed":
                return task_info["result"]
            else:
                return {"error": task_info.get("error", "Task failed")}
        return None
    
    def get_all_tasks(self) -> Dict[str, Any]:
        """Get all tasks for monitoring."""
        return {
            "active": list(self.active_tasks.values()),
            "completed": list(self.completed_tasks.values()),
            "active_count": len(self.active_tasks),
            "completed_count": len(self.completed_tasks)
        }
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """Clean up old completed tasks."""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        to_remove = []
        for task_id, task_info in self.completed_tasks.items():
            if current_time - task_info.get("completed_at", 0) > max_age_seconds:
                to_remove.append(task_id)
        
        for task_id in to_remove:
            self.completed_tasks.pop(task_id, None)
        
        return len(to_remove)


# Global background processor instance
background_processor = BackgroundProcessor()
