from services.processors.base_processor import DocumentProcessor
from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient
from fastapi import UploadFile, HTTPException
from typing import Optional
from pydantic import BaseModel, ValidationError
import os
import tempfile
from urllib.request import urlopen
from io import BytesIO
from PyPDF2 import PdfReader
import time
import re
import json
from fastapi.responses import JSONResponse
import requests
import fitz
import base64
from domain.models.papirus.tutela_fallo_model import TutelaFalloDocument
from utils.helpers import get_text_from_pdf
import asyncio  # Used for CancelledError handling

text_ex1 = """
Tutela No. 110014189033 2024 02800 00

República de Colombia
Rama Judicial del Poder
Público

JUZGADO 33 DE PEQUEÑAS CAUSAS Y CUMPETENCIA
MÚLTIPLE LOCALIDAD DE CHAPINERO
Bogotá, D.C., tres (03) de octubre de dos mil veinticuatro
(2024)

ACCIÓN DE TUTELA No. 11001-41-89-033-2024-02800-00

Accionante: LUIS CARLOS PEÑAS MONTAÑO
Accionado: BANCOLOMBIA S.A.
Asunto: Sentencia de Primera Instancia.

ASUNTO A RESOLVER

Procede el Despacho a resolver la ACCIÓN DE TUTELA de la
referencia presentada por LUIS CARLOS PEÑAS MONTAÑO, en la
que se acusa la vulneración del derecho fundamental de petición.

1. ANTECEDENTES
1.1. Hechos.
Manifestó el accionante dentro del escrito de tutela, los siguientes:
Tutela No. 110014189033 2024 02800 00



Tutela No. 110014189033 2024 02800 00


1.2. Pretensiones.
En consecuencia, pretende el accionante se amparen sus derechos
vulnerados, ordenando a  la convocada BANCOLOMBIA S.A., lo
siguiente:


Tutela No. 110014189033 2024 02800 00

1.3. Trámite Procesal.
Correspondiéndole por reparto a este Juzgado conocer de la acción,
mediante auto calendado 27 de septiembre de 2024, se admitió la
tutela, ordenándose oficiar a la accionada, y a los vinculados
SUPERINTENDENCIA FINANCIERA DE COLOMBIA y al
DEFENSOR DEL CONSUMIDOR FINANCIERO DE BANCOLOMBIA
S.A., para que se pronunciaran sobre cada uno de los hechos y
derechos que dieron origen a la presente acción constitucional.

MANUELA GOMEZ GIRALDO , en calidad de abogada de la firma
GPA LEGAL S.A.S., firma que a su vez es apoderada de la accionada
BANCOLOMBIA S.A.,  mediante respuesta  allegada a este
Despacho, manifestó lo siguiente frente a los hechos y pretensiones
de la acción de tutela:

Tutela No. 110014189033 2024 02800 00


Tutela No. 110014189033 2024 02800 00

Por parte del vinculado DEFENSOR DEL CONSUMIDOR
FINANCIERO DEL BANCOLOMBIA S.A., allego respuesta la señora
MARÍA ADELAYDA CALLE CORREA  en su calidad defensora del
consumidor financiero, y quien manifestó lo siguiente:


Tutela No. 110014189033 2024 02800 00



Tutela No. 110014189033 2024 02800 00


Tutela No. 110014189033 2024 02800 00


Tutela No. 110014189033 2024 02800 00


Por último , la vinculada SUPERINTENDENCIA FINANCIERA DE
COLOMBIA allego respuesta por intermedio de la señora JUDY
ALEXANDRA RAMIREZ CORDERO  en calidad de funcionaria de
dicha entidad, y quien manifestó lo siguiente  frente a los hechos y
pretensiones de la acción:

Tutela No. 110014189033 2024 02800 00





Tutela No. 110014189033 2024 02800 00

2. CONSIDERACIONES

La acción de tutela está consagrada para reclamar la protección
de los  derechos constitucionales de los ciudadanos, que en
principio son los enunciados por la misma Carta en el capítulo
primero del título II. Conforme a los artículos 86 de la Constitución
Política y 5° del Decreto 2591 de 1991, la acción de tutela es un
instrumento judicial de carácter constitucional, subsidiario, residual
y autónomo, dirigido a proteger los derechos fundamentales o por
conexidad de cualquier persona, cuando se  vean vulnerados o
amenazados por la acción u omisión de las autoridades y
excepcionalmente por los particulares.

A. Problema Jurídico.

El Despacho se contrae a resolver si en el caso expuesto, se
presenta vulneración a los derechos fundamentales invocados por
el accionante al endilgarle a la BANCOLOMBIA S.A., accionada,
no haber dado respuesta de fondo a la petición radicada el  19 de
julio de 2024, ante el defensor del consumidor financiero, la cual,
le fue transferida por este último, por ser de su competencia .

B. La acción de tutela y su procedencia.

Legitimación activa. La Constitución Política en su artículo  86
consagra la posibilidad de que cualquier persona puede acudir a
la acción de tutela  como mecanismo de defensa para reclamar la
protección inmediata de sus  derechos fundamentales. En el caso
concreto, el accionante LUIS CARLOS PEÑAS MONTAÑO , aduce
violación de su derecho fundamental de petición , razón por la
cual, se encuentra legitimado para presentar la acción.

Legitimación pasiva. La parte accionada, BANCOLOMBIA S.A. ,
con fundamento en lo dispuesto en el Decreto 2591 de 1991, está Tutela No. 110014189033 2024 02800 00

legitimada como parte pasiva en el presente asunto, en la medida
que se les atribuye la violación de los derechos en discusión.

C. El derecho fundamental de petición

La H. Corte Constitucional, respecto de la garantía fundamental en
comento ha sostenido que, para solicitar la protección del derecho
fundamental de petición no existe otro mecanismo de defensa
judicial, de ahí que la acción de tutela sea el único medio judicial
idóneo y eficaz para obtener su amparo de forma definitiva.1

En relación al derecho de petición que exige la accionante sea
protegido con apoyo en lo dispuesto por el artículo 23 constitucional,
vale la pena aclarar que de conformidad con el texto literal de dicha
disposición: “Toda persona  tiene derecho a presentar peticiones
respetuosas ante las au toridades por  motivos de interés general o
particular y a obtener pronta resolución. El legislador podrá
reglamentar su ejercicio ante organizaciones privadas para  garantizar
los derechos fundamentales”.

En dicho aspecto, se tiene que la Corte Constitucional, en
reiterada jurisprudencia ha ilustrado sobre las características que
posee el derecho de petición a saber:

a. Su protección podía ser solicitada mediante acción de tutela,
cuando existan actos u omisiones de la autoridad que obstruyan
el ejercicio del  derecho o no resuelvan oportunamente sobre lo
solicitado; b. No se entiende conculcado el derecho de petición
cuando la autoridad responde al peticionario, aunque la respuesta
sea negativa; c. El derecho a obtener una  pronta resolución hace

1 Al respecto, en la sentencia T-230 de 2020 se dijo que “(…) el recurso de amparo es el mecanismo de
defensa judicial idóneo y eficaz para la protección del derecho de petición, si se tiene en cuenta que, en el
ordenamiento colombiano no existe otra alternativa para proceder a su amparo” . Postura reiterada en la
sentencia T-223 de 2021. En esa misma línea, ver, entre otras, las sentencias T -149 de 2013, C- 951 de
2014, T-084 de 2015, T-138 de 2017 y T-206 de 2018, T-077 de 2018 y T-424 de 2019. Tutela No. 110014189033 2024 02800 00

parte del núcleo esencial del derecho de petición y  de aquel
depende la efectividad de este último, y d. El legislador al regular
el derecho fundamental de petición no puede afectar el núcleo
esencial del derecho consagrado en el artículo 23 de la Carta, n i
la exigencia de pronta resolución

Igualmente, la Corte Constitucional ha señalado que además de
los requisitos atrás vistos, la respuesta debe ponerse en
conocimiento del peticionario.

De igual manera, frente AL PRINCIPIO DE OFICIOSIDAD 2, nos
encontramos que la honorable Corte Constitucional, ha dicho:

El principio de oficiosidad, el cual se encuentra íntimamente
relacionado con el principio de informalidad, se traduce en el papel
activo que debe asumir el juez de tutela en la conducción del proceso,
no sólo en lo que tiene que ver con la interpretación de la solicitud de
amparo, sino también, en la búsqueda de los elementos que le
permitan comprender a cabalidad cuál es la situación que se somete
a su conocimiento, para con ello tomar una decisión de fondo que
consulte la justicia, que abarque íntegramente la problemática
planteada, y de esta forma provea una solución efectiva y adecuada,
de tal manera que se protejan de manera inmediata los derechos
fundamentales cuyo amparo se solicita si hay lugar a ello.” En
ejercicio de estas atribuciones conferidas al juez constitucional de
acuerdo con el principio de oficiosidad, es razonable que el objeto de
la acción de tutela cambie en ciertos casos, pues el juez tiene el deber
de determinar qué es lo que accionante persigue con el recurso de
amparo, con el fin de brindarle la protección más eficaz posible de
sus derechos fundamentales. Así, en ese análisis, puede encontrar
circunstancias no indicadas en el escrito de tutela sobre las que se
hace necesario su pronunciamiento.

2 Sentencia SU-108 de 2018. Tutela No. 110014189033 2024 02800 00

D. Caso concreto.

De entrada, advierte este Despacho que le asiste razón al
accionante, toda vez que, HAN TRANSCURRIDO MÁS DE 41 DÍAS
HÁBILES desde que el vinculado DEFENSOR DEL CONSUMIDOR
FINANCIERO DE BANCOLOMBIA S.A. , traslado la p etición del
accionante a la entidad bancaria hoy accionada BANCOLOMBIA
S.A., es decir, desde el 03 de agosto de 2024, a la fecha.

Si bien los apoderados de la accionada manifestaron que , el
accionante no radico la petición  directamente en el buzón de su
representada, lo cierto es que de las respuestas aportadas por parte
de las entidades vinculadas SUPERINTENDENCIA FINANCIERA
DE COLOMBIA  y DEFENSOR DEL CONSUMIDOR FINANCIERO
DE BANCOLOMBIA S.A.,  se extrae que, en efecto, la accionada
BANCOLOMBIA S.A.,  si conoció de dicha petición, inclusive,
emitiendo una respuesta:

Tutela No. 110014189033 2024 02800 00

De igual manera, es importante precisar que, por un lado, no se
aporto constancia de haberse puesto en conocimiento dicha
respuesta al hoy accionante, el señor LUIS CARLOS PEÑAS
MONTAÑO, y de igual manera, resaltar que la respuesta de la
accionada, allegada al trámite por parte de las entidades
vinculadas, se encuentra dirigida al padre (Q.E.P.D.) del accionante.

Por todo lo anterior, el D espacho considera que debe hacer us o del
PRINCIPIO DE OFICIOSIDAD , por lo que, se ORDENARÁ a la
accionada BANCOLOMBIA S.A.,  que por intermedio de sus
representantes legales y/o persona encargada del cumplimiento de
los fallos de tutela, para que, dentro de las 48 horas siguientes a la
notificación de este fallo, de respuesta de fondo o definitiva,
congruente a lo pedido y con la debida notificación, a l a
petición elevada el 19 de julio de 2024, por parte del señor LUIS
CARLOS PEÑAS MONTAÑO , y que le fue trasladada el 03 de
agosto de 2024, por parte del DEFENSOR DEL CONSUMIDOR
FINANCIERO DE BANCOLOMBIA S.A. ; aportando a este despacho
los respectivos compr obantes y/o soportes del acatamiento del
presente fallo de tutela.

Por último, s e ORDENA la DESVINCULACIÓN del DEFENSOR
DEL CONSUMIDOR FINANCIERO DE BANCO LOMBIA S.A., y de
la SUPERINTENDENCIA FINANCIERA DE COLOMBIA.

DECISIÓN

En mérito de lo expuesto, el JUZGADO TREINTA Y TRES DE
PEQUEÑAS CAUSAS Y COMPETENCIA MÚLTIPLE DE BOGOTÁ
D.C.- LOCALIDAD DE  CHAPINERO, administrando justicia en
nombre de la República de Colombia y por autoridad de la Ley,


 Tutela No. 110014189033 2024 02800 00

RESUELVE:

PRIMERO: AMPARAR el derecho fundamental de petición del
señor LUIS CARLOS PEÑAS MONTAÑO  de conformidad a lo
esbozado en la parte motiva de esta providencia.

SEGUNDO: ORDENAR a la accionada BANCOLOMBIA S.A.,  que
por intermedio de sus representantes legales y/o persona
encargada del cumplimiento de los fallos de tutela, para que, dentro
de las 48 horas siguientes a la notificación de este fallo, de
respuesta de fondo o definitiva, congruente a lo pedido y con la
debida notificación, a la petición elevada el 19 de julio de 2024, por
parte del señor LUIS CARLOS PEÑAS MONTAÑO, y que le fue
trasladada el 03 de agosto de 2024, por parte del DEFENSOR DEL
CONSUMIDOR FINANCIERO DE BANCOLOMBIA S.A.;  aportando a
este despacho los respectivos compr obantes y/o soportes del
acatamiento del presente fallo de tutela.

TERCERO: NOTIFICAR esta determinación a los intervinientes en
la forma más rápida y eficaz, conforme lo ordena el artículo 30 del
Decreto 2591 de 1991.

CUARTO: REMITIR las diligencias a la Corte Constitucional para
su eventual revisión, en caso de no ser impugnada esta decisión.

NOTIFÍQUESE Y CUMPLASE,

FERNANDO MORENO OJEDA
Juez

JCGM Firmado Por:
Fernando  Moreno   Ojeda
Juez
Juzgado Pequeñas Causas
Juzgado 033 Pequeñas Causas Y Competencia Múltiple
Bogotá, D.C. - Bogotá D.C.,

Este documento fue generado con firma electrónica y cuenta con plena validez jurídica,
conforme a lo dispuesto en la Ley 527/99 y el decreto reglamentario 2364/12

Código de verificación: 4d94d6d79704f55e4cdfd27d77006cd0011d4dc248cf8bd23271606dfa0ff94f
Documento generado en 03/10/2024 04:07:36 PM

Descargue el archivo y valide éste documento electrónico en la siguiente URL:
https://procesojudicial.ramajudicial.gov.co/FirmaElectronica
"""

prompt = f"""
Eres un abogado que procesa información de documentos judiciales para determinar si el fallo de una tutela beneficia o no a BANCOLOMBIA. A continuación, identifica los siguientes elementos del documento "Review_text":

1. **Juzgado**: Nombre completo del juzgado que emite la decisión.
   - Suele aparecer en el encabezado o en las primeras líneas del texto.

2. **Fecha**: Fecha en que se presenta el documento legal.
   - Formato: "dd/mm/aaaa"
   - Usa la primera fecha que se encuentre en el texto. Si no hay fecha, devuelve cadena vacía ("").

3. **Radicado**: Número de expediente, con al menos 18 dígitos y posiblemente con guiones.
   - Puede verse como "Radicación", "RADICACIÓN", "Rad", "RAD", "Radicado No.", etc.
   - Debes eliminar guiones ("-") y espacios. Ejemplo: "11001 41 89 033 2024-02800-00" → "11001418903320240280000"
   - Si no se encuentra,  devuelve cadena vacía ("").

4. **Accionante**: Nombre o razón social de la persona/entidad que presenta la tutela.
   - Evita confundirlo con el "Accionado".
   - Si está identificado con "CC" o "NIT", elimina esos identificadores. Ejemplo: "JOSÉ PÉREZ CC 12345678" → "JOSÉ PÉREZ"

5. **Accionado**: Lista de nombres o entidades demandadas en la tutela (ej. BANCOLOMBIA S.A.).
   - Si no hay ninguno, devuelve un arreglo vacío "[]".

6. **Fallo/Decision**: Estado final de la demanda respecto a BANCOLOMBIA. Únicamente puede ser:
   - `"favorable"` si el juez NIEGA la tutela, la declara IMPROCEDENTE o NO PROCEDE contra BANCOLOMBIA.
   - `"desfavorable"` si el juez CONCEDE, RECONOCE, AMPARA, ORDENA o EXHORTA a BANCOLOMBIA (es decir, obliga a la entidad).
   - `"null"` si no se puede determinar.

7. **Termino**: Tiempo concedido (horas o días) para cumplir la orden.
   - Búscalo en la sección "RESUELVE", "DECISIÓN", "CONCLUSIÓN" o "FALLA".
   - Ejemplo: "48 horas" o "10 días".
   - Si no hay ningún término,  devuelve cadena vacía ("").

8. **Correo**: Lista de todos los correos electrónicos presentes en el texto.
   - Cualquier cadena que contenga "@".
   - Elimina duplicados.
   - Si no hay correos, devuelve un arreglo vacío "[]".

---

    **Ejemplo con texto:**

    Texto:
    {text_ex1}

    **Salida esperada:**

    "termino": "48 horas",
    "juzgado": "JUZGADO 33 DE PEQUEÑAS CAUSAS Y COMPETENCIA MÚLTIPLE LOCALIDAD DE CHAPINERO",
    "radicado": "11001418903320240280000",
    "accionante": "LUIS CARLOS PEÑAS MONTAÑO",
    "accionado": ["BANCOLOMBIA S.A."],
    "decision": "desfavorable",
    "correo": [
        "<EMAIL>",
        "<EMAIL>"
    ],
    "fecha_documento": "03/10/2024"

        """


class TutelaFalloProcessor(DocumentProcessor):
    def __init__(self, openai_client: OpenAIClient,  langchain_client: LangChainClient):
        self.openai_client = openai_client
        self.langchain_client = langchain_client

    async def process(self, file: Optional[UploadFile], data: Optional[str] = None) -> dict:
        if not file and not data:
            raise HTTPException(status_code=400, detail="The CV action requires a file or URL")
        result = await self.process_tutela(file, data)
        return result

    async def process_tutela(self, file: UploadFile, data: Optional[str] = None) -> dict:
        """
        Process a tutela document. If 'data' is a URL to a PDF, it downloads and processes it.
        If 'file' is provided, it reads the uploaded file.

        Raises:
            HTTPException: For various processing errors
            asyncio.CancelledError: If the task is cancelled
        """
        # Track temporary files for cleanup in case of cancellation
        temp_files = []

        # Crear un directorio temporal para almacenar los archivos
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                if data:
                    # Descargar el archivo desde la URL proporcionada en 'data'
                    url = data.strip()
                    response = requests.get(url)
                    if response.status_code != 200:
                        raise HTTPException(status_code=400, detail="No se pudo descargar el archivo desde la URL proporcionada.")
                    # Obtener el nombre del archivo desde la URL
                    filename = url.split("/")[-1].split("?")[0]
                    file_extension = filename.split(".")[-1].lower()

                    pdf_path = os.path.join(temp_dir, filename)
                    with open(pdf_path, "wb") as f:
                        f.write(response.content)
                    # Track the temporary file
                    temp_files.append(pdf_path)

                else:
                    # Usar el archivo subido
                    filename = file.filename
                    file_extension = filename.split(".")[-1].lower()

                    pdf_path = os.path.join(temp_dir, filename)
                    file_content = await file.read()
                    with open(pdf_path, "wb") as f:
                        f.write(file_content)
                    # Track the temporary file
                    temp_files.append(pdf_path)

                if file_extension == 'pdf':
                    # Guardar el PDF en el directorio temporal
                    pdf_path = os.path.join(temp_dir, filename)

                    CV_tot_text = await get_text_from_pdf(pdf_path)

                    os.remove(pdf_path)


                else:
                    raise HTTPException(status_code=400, detail="El archivo debe ser un PDF.")

                CV_data = await self.get_structured_data(CV_tot_text, TutelaFalloDocument)

                return CV_data

            except asyncio.CancelledError:
                # Clean up any temporary files that might still exist
                for temp_file in temp_files:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except Exception as cleanup_error:
                            print(f"Error cleaning up file {temp_file}: {cleanup_error}")
                # Re-raise to propagate the cancellation
                raise

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error al procesar el CV: {str(e)}")


    async def get_structured_data(self, tutela_text: str, tutela_model: type[BaseModel]):
        """
        Extract structured data from tutela text using language model.

        Args:
            tutela_text (str): Processed tutela text
            tutela_model (type[BaseModel]): Pydantic model to structure the data

        Returns:
            dict: Structured tutela data

        Raises:
            HTTPException: Passed through from LangChainClient
        """
        data = f"Here is the Review_text: {tutela_text}"
        correos_cendoj = await self.extraer_correos_cendoj(tutela_text)

        # Call the langchain_client's get_structured_data method
        # The helper function handles all validation, retries, and error cases
        response = await self.langchain_client.get_structured_data(tutela_model, data, prompt)

        # Extract the response content and add the correos_cendoj and token_usage
        response_data = response['response']
        result = {**response_data, "correo": correos_cendoj}
        result["token_usage"] = response['token_usage']

        return result


    async def extraer_correos_cendoj(self, texto: str) -> list:
        """
        Extrae todos los correos únicos que terminan en '@cendoj.ramajudicial.gov.co' de un texto.

        Parámetros:
            texto (str): El texto completo donde se buscarán los correos.

        Retorna:
            list: Lista de correos únicos ordenados alfabéticamente.
        """
        patron_correos = r'[a-zA-Z0-9._%+-]+@cendoj\.ramajudicial\.gov\.co'
        correos = re.findall(patron_correos, texto)
        correos_unicos = sorted(set(correos))
        return correos_unicos