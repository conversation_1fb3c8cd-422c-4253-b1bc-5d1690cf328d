import os
import time
import asyncio
import logging
import psutil

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse, FileResponse
from dotenv import load_dotenv
from logging_config import setup_logging

# Load environment variables and configure logging
load_dotenv()
logger = setup_logging()

# --------------------------------------------------------------------
# Environment variables verification
# --------------------------------------------------------------------

API_KEY = os.getenv("API_KEY")
API_VERSION = os.getenv("API_VERSION")
AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT")
MODEL = os.getenv("MODEL")
MAX_CONCURRENT_TASKS = int(os.getenv("MAX_CONCURRENT_TASKS", "4"))

print(MODEL)
print(API_VERSION)


if not all([API_KEY, API_VERSION, AZURE_ENDPOINT, MODEL]):
    missing_vars = [
        var for var, value in {
            "API_KEY": API_KEY,
            "API_VERSION": API_VERSION,
            "AZURE_ENDPOINT": AZURE_ENDPOINT,
            "MODEL": MODEL
        }.items() if not value
    ]
    logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
    raise Exception("Missing .env variables")

# --------------------------------------------------------------------
# FastAPI application initialization
# --------------------------------------------------------------------

root_path = os.environ.get("ROOT_PATH", "")
app = FastAPI(root_path=root_path)



# --------------------------------------------------------------------
# CORS configuration
# --------------------------------------------------------------------

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --------------------------------------------------------------------
# Global request logging middleware
# --------------------------------------------------------------------

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """
    Logs each incoming request and its processing time.
    """
    start_time = time.time()

    try:
        response = await call_next(request)
        process_time = time.time() - start_time

        logger.info(
            f"Request: {request.method} {request.url} - Status: {response.status_code} - Time: {process_time:.2f}s"
        )

        response.headers["X-Process-Time"] = f"{process_time:.2f}"
        return response

    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"Error processing {request.method} {request.url} - {str(e)} - Time: {process_time:.2f}s",
            exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={"message": "Internal server error"}
        )

# --------------------------------------------------------------------
# Shared clients and resources
# --------------------------------------------------------------------

from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient

openai_client = OpenAIClient(
    api_key=API_KEY,
    api_version=API_VERSION,
    azure_endpoint=AZURE_ENDPOINT,
    model=MODEL
)

langchain_client = LangChainClient(
    api_key=API_KEY,
    api_version=API_VERSION,
    azure_endpoint=AZURE_ENDPOINT,
    model=MODEL
)

# Semaphore for concurrency control and active tasks registry
semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)
active_tasks = {}

app.state.logger = logger
app.state.openai_client = openai_client
app.state.langchain_client = langchain_client
app.state.semaphore = semaphore
app.state.active_tasks = active_tasks

# --------------------------------------------------------------------
# Routers
# --------------------------------------------------------------------

from routes.process import router as process_router
from routes.process_test import router as process_test_router
from routes.health import router as health_router
from routes.maintenance import router as maintenance_router
from routes.webhook_test import router as webhook_test_router
from routes.task_management import router as task_management_router

app.include_router(process_router)
app.include_router(health_router)
app.include_router(maintenance_router)
app.include_router(webhook_test_router)
app.include_router(process_test_router)
app.include_router(task_management_router)

