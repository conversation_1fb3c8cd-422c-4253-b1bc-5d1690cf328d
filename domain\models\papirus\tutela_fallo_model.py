from pydantic import BaseModel, Field
from typing import List

class TutelaFalloDocument(BaseModel):
    termino: str = Field(default="", description="El término (en horas o días) declarado por el juzgado para cumplir con el fallo. Si no hay ninguno, se utiliza cadena vacía ('').")
    juzgado: str = Field(default="", description="El juzgado que presenta el documento legal. Si no se encuentra, devuelve cadena vacía ('').")
    radicado: str = Field(default="", description="El número de proceso o código del documento. Retira los guiones y espacios. Ejemplo: '1000 1234-2024' → '100012342024'. Si no se encuentra, usar cadena vacía ('').")
    accionante: str = Field(default="", description="La persona o empresa que solicita la acción de tutela. Si contiene CC o NIT, elimínalos. Si no se encuentra, usar cadena vacía ('').")
    accionado: List[str] = Field(default_factory=list, description="Lista de personas o empresas señaladas por el accionante. Si no se encuentra, devolver lista vacía ([]).")
    decision: str = Field(default="", description="La decisión a la que llega el juzgado: 'favorable', 'desfavorable' o 'null'. Definido desde la perspectiva de BANCOLOMBIA. Si no se puede determinar, usar cadena vacía ('').")
    correo: List[str] = Field(default_factory=list, description="Lista de correos electrónicos presentes en el documento. Si no hay correos, usar lista vacía ([]).")
    fecha_documento: str = Field(default="", description="La fecha en que se emitió el documento. Formato 'dd/mm/aaaa'. Si no se encuentra, usar cadena vacía ('').")
